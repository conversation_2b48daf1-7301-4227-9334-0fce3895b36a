<?php
namespace app\admin\controller\agent;

use app\admin\logic\finance\ShopWithdrawalLogic;
use think\facade\Db;
use think\facade\View;
use app\common\model\agent\Agent as AgentModel;
use app\common\model\agent\AgentMerchantfees;
use app\admin\logic\agent\AgentLogic;
use app\admin\logic\agent\AgentDepositLogic;
use app\common\basics\AdminBase;
use app\common\server\JsonServer;
use app\common\logic\OrderRefundLogic;

class Agent extends AdminBase
{

    /*
     * 代理列表
     *
     */
     public function index(){
         if ($this->request->isPost()) {
             $params = $this->request->post();
             $result = AgentLogic::lists($params);
             return JsonServer::success('', $result);
         }
         $levels = ['一级代理', '二级代理', '三级代理'];
         return view('agent/index', ['levels' => $levels]);
     }

    /**
     * 获取不同类型的用户列表
     * @return \think\response\Json
     */
    public function getUsersByType()
    {
        $user_id = $this->request->get('user_id', 0);
        $type = $this->request->get('type', 0);
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);

        $data = AgentLogic::getUsersByType($user_id, $type, $page, $limit);
        return JsonServer::success('获取成功', $data);
    }

    /*
     * 代理结算
     * @notes 代理结算
     */
    public function settle()
    {
        if($this->request->isAjax()){
            $get= $this->request->post();
            $lists = AgentLogic::settle($get);
            return JsonServer::success('获取成功', $lists);
        }

        $statistics = AgentLogic::statistics();
        View::assign('statistics', $statistics);
        return view('agent/settle');
    }

    /**
     * @Notes: 代理结算记录
     * @Author: 张无忌
     */
    public function settlementRecord()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            $lists = AgentLogic::record($get);
            return JsonServer::success('获取成功', $lists);
        }

        $shop_id = $this->request->get('agent_id');
        $statistics = AgentLogic::statistics($shop_id);
        View::assign('agent_id', $shop_id);
        View::assign('statistics', $statistics);
        return view('agent/settlement_record');
    }
    /**
     * @notes 开通代理
     * @return \think\response\View
     * <AUTHOR>
     * @date 2021/9/2 19:32
     */
    public function open()
    {
        if($this->request->isPost()) {
            $params = $this->request->post();
//             $a=AgentLogic::insertRandomUsers(50);
//             var_dump($a);die;
            $result = AgentLogic::open($params);
            if($result) {
                return JsonServer::success('开通成功');
            }
            return JsonServer::error(AgentLogic::getError());
        }
//        $levels = DistributionLevelLogic::getLevels();
        return view('agent/open');
    }


    /**
     * @notes 用户列表
     * @return \think\response\Json|\think\response\View
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/9/3 11:50
     */
    public function userLists()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post();
            $lists = AgentLogic::getUserLists($params);
            return JsonServer::success('', $lists);
        }
        return view('agent/user_lists');
    }


    /**
     * @notes 代理调整
     * @return \think\response\Json|\think\response\View
     * <AUTHOR>
     * @date 2021/9/3 14:10
     */
    public function adjust()
    {
        if($this->request->isPost()) {
            $params = $this->request->post();
            $result = AgentLogic::adjust($params);
            if($result) {
                return JsonServer::success('调整成功');
            }
            return JsonServer::error(AgentLogic::getError());
        }
        $params = $this->request->get();
        $user = AgentLogic::getUser($params);
        return view('agent/adjust', [
            'user' => $user
        ]);
    }

    /**
     * @notes 代理配置
     */
    public function setting()
    {

        if ($this->request->isAjax()) {
            $post = $this->request->post();

            $aa=AgentLogic::setConfig($post);

            return $aa;
        }
        $config = AgentLogic::getConfig();
        return view('agent/setting', ['config' => $config]);
    }
    /**
     * @Notes: 商家结算详细记录
     * @Author: 张无忌
     */
    public function settlementDetail()
    {
        $settle_id = $this->request->get('id');
        $get = $this->request->get();
        $lists = AgentLogic::detail($get);

        View::assign('id', $settle_id);
        View::assign('detail', $lists);
        return view('agent/settlement_detail');
    }
    /*
    * 取消代理
    */
    public function isOrderFreeze()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $result = AgentLogic::audit($post);
            if (false === $result) {
                return JsonServer::error(AgentLogic::getError() ?: '操作失败');
            }
            return JsonServer::success('操作成功');
        }
        $id = $this->request->get();
        $detail=AgentLogic::detail($id);
        if(!isset($detail['detal'])){
            return JsonServer::error( '信息失效');
        }
        return view('agent/audit', [
            'detail' => $detail
        ]);

    }
    /*
     * 取消代理
     */
    public function isFreeze()
    {
        $post = $this->request->post();
        $result=AgentLogic::freeze($post);
        if($result) {
            return JsonServer::success('调整成功');
        }

    }


    /**
     * @Notes: 商家提现列表
     * @Author: 张无忌
     */
    public function withdrawal()
    {
        if($this->request->isAjax()){
            $get= $this->request->get();
            $lists = AgentLogic::withdrawalists($get);
            return JsonServer::success('获取成功', $lists);
        }

        View::assign('summary', AgentLogic::summary());
        View::assign('statistics', AgentLogic::statistics2());
        return view('agent/withdrawal');
    }

    /**
     * @Notes: 商家提现详细
     * @Author: 张无忌
     * @return \think\response\View
     */
    public function withdrawalDetail()
    {
        $id = $this->request->get('id');
        View::assign('detail', AgentLogic::Agentdetail($id));
        return view('agent/withdrawal_detail');
    }

    /**
     * @Notes: 商家提现统计
     * @Author: 张无忌
     */
    public function withdrawalStatistics()
    {
        if ($this->request->isAjax()) {
            $statistics = ShopWithdrawalLogic::statistics();
            return JsonServer::success('获取成功', $statistics);
        }

        return JsonServer::error('请求异常');
    }

    /**
     * @Notes: 审核商家提现
     * @Author: 张无忌
     */
    public function withdrawalExamine()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $res = AgentLogic::examine($post);
            if ($res === false) {
                $error = AgentLogic::getError() ?: '审核失败';
                return JsonServer::error($error);
            }

            return JsonServer::success('审核成功');
        }

        return view('agent/withdrawal_examine');
    }

    /**
     * @Notes: 商家提现转账
     * @Author: 张无忌
     */
    public function withdrawalTransfer()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $res = AgentLogic::transfer($post);
            if ($res === false) {
                $error = AgentLogic::getError() ?: '审核失败';
                return JsonServer::error($error);
            }

            return JsonServer::success('审核成功');
        }

        $id = $this->request->get('id');
        View::assign('detail', AgentLogic::Agentdetail($id));
        return view('agent/withdrawal_transfer');
    }

    /**
     * @notes 在线转账
     * @return \think\response\Json|void
     * <AUTHOR>
     * @datetime 2023-06-07 09:48:22
     */
    function WithdrawalTransferOnline()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $res = AgentLogic::transfer_online($post);
            if ($res === false) {
                $error = AgentLogic::getError() ? : '在线转账失败';
                return JsonServer::error($error);
            }

            return JsonServer::success(AgentLogic::getError() ? : '在线转账成功');
        }
    }

    /**
     * @notes 代理保证金管理
     * @return \think\response\Json|\think\response\View
     * <AUTHOR> @date
     */
    public function deposit()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post();
            $result = AgentLogic::depositList($params);
            return JsonServer::success('', $result);
        }
        return view('agent/deposit');
    }

    /**
     * @notes 代理保证金退款
     * @return \think\response\Json
     * <AUTHOR> @date
     */
    public function refundDeposit()
    {
        if ($this->request->isPost()) {
            $id = $this->request->post('id');
            if (empty($id)) {
                return JsonServer::error('参数错误');
            }

            $result = AgentLogic::refundDeposit($id, $this->adminId);
            if ($result === false) {
                return JsonServer::error(AgentLogic::getError() ?: '退款失败');
            }
            return JsonServer::success('退款成功');
        }
        return JsonServer::error('请求方式错误');
    }

    /**
     * @notes 代理保证金手动退款页面（银行卡/支付宝）
     * @return \think\response\View
     * <AUTHOR> @date
     */
    public function manualRefund()
    {
        if ($this->request->isPost()) {
            $post = $this->request->post();

            // 验证参数
            if (empty($post['id']) || empty($post['refund_id']) ||
                empty($post['payment_type']) || empty($post['transfer_voucher'])) {
                return JsonServer::error('参数错误');
            }

            // 根据支付方式验证参数
            if ($post['payment_type'] == 1 && empty($post['bank_id'])) {
                return JsonServer::error('请选择银行卡');
            }

            if ($post['payment_type'] == 2 && empty($post['alipay_id'])) {
                return JsonServer::error('请选择支付宝账户');
            }

            // 添加管理员ID
            $post['admin_id'] = $this->adminId;

            // 调用逻辑层处理手动退款
            $result = AgentLogic::manualRefund($post);
            if ($result === true) {
                return JsonServer::success('手动退款操作成功');
            }
            return JsonServer::error(AgentLogic::getError() ?: '手动退款失败');
        }

        $id = $this->request->get('id/d');
        if (empty($id)) {
            return JsonServer::error('参数错误');
        }

        // 获取保证金记录
        $deposit = \app\common\model\agent\AgentMerchantfees::where('id', $id)->find();
        if (!$deposit) {
            return JsonServer::error('保证金记录不存在');
        }

        // 检查状态是否为退款申请中或退款失败
        if ($deposit['status'] != 3 && $deposit['status'] != 5) {
            return JsonServer::error('只有退款申请中或退款失败的保证金才能进行手动退款操作');
        }

        // 获取用户信息
        $user = \app\common\model\user\User::where('id', $deposit['user_id'])->find();
        if (!$user) {
            return JsonServer::error('用户不存在');
        }

        // 获取退款记录
        $refund = Db::name('common_refund')
            ->where('source_id', $id)
            ->where('refund_type', 4) // 4-代理保证金
            ->find();

        // 如果退款记录不存在，则创建一条新的退款记录
        if (!$refund) {
            // 创建退款记录
            $refundData = [
                'refund_sn' => 'RF' . date('YmdHis') . mt_rand(1000, 9999),
                'refund_type' => 4, // 4-代理保证金
                'source_id' => $id,
                'user_id' => $deposit['user_id'],
                'refund_amount' => $deposit['amount'],
                'total_amount' => $deposit['amount'],
                'payment_method' => $deposit['payment_method'] ?? '微信支付',
                'transaction_id' => $deposit['transaction_id'] ?? '',
                'refund_status' => 0, // 0-退款中
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $refundId = Db::name('common_refund')->insertGetId($refundData);
            if (!$refundId) {
                return JsonServer::error('创建退款记录失败');
            }

            // 重新获取退款记录
            $refund = Db::name('common_refund')->where('id', $refundId)->find();
        }

        // 获取代理ID
        $agent_id = Db::name('agent')->where('user_id', $deposit['user_id'])->value('id');
      
        // 获取银行卡信息
        $bank_cards = Db::name('agent_bank')
            ->where('agent_id', $agent_id)
            ->where('del', 0)
            ->select()
            ->toArray();
          
        // 获取支付宝账户信息
        $alipay_accounts = Db::name('agent_alipay')
            ->where('agent_id', $agent_id)
            ->where('del', 0)
            ->select()
            ->toArray();
         
        // 传递数据到视图
        View::assign('deposit', $deposit);
        View::assign('user', $user);
        View::assign('refund', $refund);
        View::assign('bank_cards', $bank_cards);
        View::assign('alipay_accounts', $alipay_accounts);
        View::assign('bank_cards_json', json_encode($bank_cards));
        View::assign('alipay_accounts_json', json_encode($alipay_accounts));

        return view('agent/manual_refund');
    }

    /**
     * @notes 代理保证金明细列表
     * @return \think\response\Json|\think\response\View
     * <AUTHOR> @date
     */
    public function depositDetails()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post();
            $result = AgentDepositLogic::depositDetailsList($params);
            // 直接返回数据，不使用JsonServer封装
            return json([
                'code' => 1,
                'msg' => '',
                'count' => $result['count'],
                'lists' => $result['lists']
            ]);
        }

        // 获取保证金ID
        $deposit_id = $this->request->get('deposit_id', 0);
        $user_id = $this->request->get('user_id', 0);
        $is_popup = $this->request->get('is_popup', 0);

        // 获取保证金信息
        $deposit = [];
        if ($deposit_id) {
            $deposit = Db::name('agent_merchantfees')->where('id', $deposit_id)->find();

            // 计算当前余额
            if ($deposit) {
                $current_balance = AgentDepositLogic::calculateCurrentBalance($deposit_id);
                $deposit['current_balance'] = number_format($current_balance, 2);
            }
        }

        // 获取用户信息
        $user = [];
        if ($user_id) {
            $user = Db::name('user')->where('id', $user_id)->field('id, nickname, sn, mobile')->find();
        }

        View::assign('deposit', $deposit);
        View::assign('user', $user);
        View::assign('deposit_id', $deposit_id);
        View::assign('user_id', $user_id);
        View::assign('is_popup', $is_popup);

        // 根据是否为弹窗模式选择不同的视图模板
        if ($is_popup) {
            return view('agent/deposit_details_popup');
        } else {
            return view('agent/deposit_details');
        }
    }

    /**
     * @notes 调整代理保证金
     * @return \think\response\Json|\think\response\View
     * <AUTHOR> @date
     */
    public function adjustDeposit()
    {
        if ($this->request->isPost()) {
            $post = $this->request->post();

            // 添加管理员ID
            $post['admin_id'] = $this->adminId;

            // 调用逻辑层处理保证金调整
            $result = AgentDepositLogic::adjustDeposit($post);
            if ($result === true) {
                return JsonServer::success('保证金调整成功');
            }
            return JsonServer::error(AgentDepositLogic::getError());
        }

        $deposit_id = $this->request->get('deposit_id', 0);
        $user_id = $this->request->get('user_id', 0);
        $is_popup = $this->request->get('is_popup', 0);

        // 获取保证金信息
        $deposit = [];
        if ($deposit_id) {
            $deposit = Db::name('agent_merchantfees')->where('id', $deposit_id)->find();

            // 计算当前余额
            $changes_sum = Db::name('agent_deposit_details')
                ->where('deposit_id', $deposit_id)
                ->sum('deposit_change');

            $current_balance = $deposit['amount'] + floatval($changes_sum);
            $current_balance = max(0, $current_balance); // 确保余额不为负数

            $deposit['current_balance'] = number_format($current_balance, 2);
        }

        // 获取用户信息
        $user = [];
        if ($user_id) {
            $user = Db::name('user')->where('id', $user_id)->field('id, nickname, sn, mobile')->find();
        }

        View::assign('deposit', $deposit);
        View::assign('user', $user);
        View::assign('deposit_id', $deposit_id);
        View::assign('user_id', $user_id);
        View::assign('is_popup', $is_popup);

        // 根据是否为弹窗模式选择不同的视图模板
        if ($is_popup) {
            return view('agent/adjust_deposit_popup');
        } else {
            return view('agent/adjust_deposit');
        }
    }


}