# 代理列表与代理保证金合并说明

## 修改概述
将原本分离的"代理列表"和"代理保证金"两个功能合并为一个统一的"代理列表"页面，用户可以在一个页面中查看代理信息和保证金信息，并进行相关操作。

## 主要修改内容

### 1. 后端逻辑层修改 (app/admin/logic/agent/AgentLogic.php)
在代理列表查询方法 `lists()` 中添加了保证金信息的查询：

**新增字段：**
- `deposit_amount` - 保证金金额
- `deposit_current_balance` - 当前余额  
- `deposit_status` - 保证金状态
- `deposit_id` - 保证金ID
- `payment_date` - 支付时间
- `publicity_period_end_time` - 公示期结束时间
- `deposit_status_text` - 保证金状态描述

**状态映射：**
- 0: 未支付
- 1: 已支付
- 2: 公示期结束(可退)
- 3: 退款申请中
- 4: 已退款
- 5: 退款失败

### 2. 前端页面修改 (app/admin/view/agent/index.html)

**新增表格列：**
- 保证金金额
- 当前余额
- 保证金状态

**新增操作按钮：**
- 微信退款 (状态为退款申请中时显示)
- 手动退款 (状态为退款申请中或退款失败时显示)
- 保证金明细 (所有记录都显示)

**新增模板：**
- `#deposit-status` - 保证金状态显示模板，使用不同颜色的徽章显示状态

### 3. 事件处理
**新增事件处理：**
- `refund` - 微信退款，调用 `agent.agent/refundDeposit` 接口
- `manualRefund` - 手动退款，打开手动退款页面
- `depositDetails` - 保证金明细，打开保证金明细弹窗

## 功能特点

### 1. 统一界面
- 用户可以在一个页面中查看代理的基本信息、统计数据和保证金信息
- 操作按钮根据保证金状态动态显示

### 2. 保持原有功能
- 保留了原有的代理设置、冻结/恢复资格功能
- 保留了用户数量、会员数量、商家数量、代理数量的统计和查看功能

### 3. 新增保证金管理
- 直接在列表中显示保证金金额和状态
- 提供快捷的退款操作入口
- 提供详细的保证金明细查看功能

## 使用说明

1. **查看保证金信息**：在代理列表中可以直接看到每个代理的保证金金额、当前余额和状态

2. **退款操作**：
   - 当保证金状态为"退款申请中"时，显示"微信退款"按钮
   - 当保证金状态为"退款申请中"或"退款失败"时，显示"手动退款"按钮

3. **查看明细**：点击"保证金明细"按钮可以查看该代理的保证金变动详情

## 注意事项

1. 保证金信息是通过查询 `agent_deposit` 表获取的，取最新的一条记录
2. 如果代理没有保证金记录，相关字段会显示为0或"未支付"状态
3. 操作列宽度已调整为280px以容纳更多按钮
4. 退款功能使用了现有的控制器方法，确保功能的一致性

## 技术实现

- **数据查询优化**：在原有的代理列表查询中增加了保证金信息的关联查询
- **前端模板**：使用Layui的模板语法实现动态显示
- **事件处理**：扩展了原有的表格事件处理机制
- **接口复用**：复用了现有的保证金管理接口，避免重复开发
