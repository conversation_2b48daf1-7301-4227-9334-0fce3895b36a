# 代理列表保证金功能修复说明

## 问题描述
在合并代理列表和代理保证金功能时，出现了数据库表不存在的错误：
```
SQLSTATE[42S02]: Base table or view not found: 1146 Table 'kshop.ls_agent_deposit' doesn't exist
```

## 问题原因
代码中查询的是 `agent_deposit` 表，但实际系统中使用的是 `agent_merchantfees` 表来存储代理保证金信息。

## 修复内容

### 1. 修正数据表名称
**文件：** `app/admin/logic/agent/AgentLogic.php`

**修改前：**
```php
$deposit = Db::name('agent_deposit')
    ->where('user_id', $item['user_id'])
    ->order('id', 'desc')
    ->find();
```

**修改后：**
```php
$deposit = Db::name('agent_merchantfees')
    ->where('user_id', $item['user_id'])
    ->order('id', 'desc')
    ->find();
```

### 2. 修正字段映射
根据 `agent_merchantfees` 表的实际字段结构调整了字段映射：

**表结构：**
- `id` - 主键
- `user_id` - 用户ID
- `amount` - 保证金金额
- `payment_date` - 支付时间
- `status` - 状态 (0:未支付, 1:已支付, 2:退款中, 3:已退款)
- `publicity_period_end_time` - 公示期结束时间

### 3. 修正状态映射
**修改前的状态：**
- 0: 未支付
- 1: 已支付
- 2: 公示期结束(可退)
- 3: 退款申请中
- 4: 已退款
- 5: 退款失败

**修改后的状态（符合实际表结构）：**
- 0: 未支付
- 1: 已支付
- 2: 退款中
- 3: 已退款

### 4. 修正前端显示
**文件：** `app/admin/view/agent/index.html`

**状态显示模板：**
```html
<script type="text/html" id="deposit-status">
    {{#  if(d.deposit_status == 0){ }}
    <span class="layui-badge layui-bg-gray">未支付</span>
    {{#  } else if(d.deposit_status == 1){ }}
    <span class="layui-badge layui-bg-green">已支付</span>
    {{#  } else if(d.deposit_status == 2){ }}
    <span class="layui-badge layui-bg-orange">退款中</span>
    {{#  } else if(d.deposit_status == 3){ }}
    <span class="layui-badge layui-bg-cyan">已退款</span>
    {{#  } else { }}
    <span class="layui-badge layui-bg-gray">未知状态</span>
    {{#  } }}
</script>
```

**操作按钮显示条件：**
```html
{{#  if(d.deposit_status == 2){ }}
<a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="refund">微信退款</a>
<a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="manualRefund">手动退款</a>
{{#  } }}
{{#  if(d.deposit_id > 0){ }}
<a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="depositDetails">保证金明细</a>
{{#  } }}
```

## 修复后的功能

### 1. 数据显示
- ✅ 保证金金额：显示 `amount` 字段
- ✅ 当前余额：暂时使用原金额（后续可优化为计算实际余额）
- ✅ 保证金状态：根据 `status` 字段显示对应状态
- ✅ 支付时间：格式化显示 `payment_date`
- ✅ 公示期结束时间：格式化显示 `publicity_period_end_time`

### 2. 操作功能
- ✅ 微信退款：当状态为"退款中"时显示
- ✅ 手动退款：当状态为"退款中"时显示
- ✅ 保证金明细：当有保证金记录时显示

### 3. 状态管理
- 🔘 **未支付** - 灰色徽章
- 🟢 **已支付** - 绿色徽章
- 🟠 **退款中** - 橙色徽章
- 🔵 **已退款** - 青色徽章

## 注意事项

1. **当前余额计算**：目前简化处理，使用原金额作为当前余额。如需精确计算，需要结合 `agent_deposit_details` 表的明细记录。

2. **退款功能**：使用了现有的 `refundDeposit` 和 `manualRefund` 方法，确保功能一致性。

3. **数据兼容性**：修复后的代码兼容现有的数据结构，不需要额外的数据迁移。

## 测试建议

1. 访问代理列表页面，确认页面正常加载
2. 检查保证金信息是否正确显示
3. 测试各种状态下的操作按钮显示
4. 验证退款和明细查看功能

现在代理列表页面应该可以正常显示，并且包含了完整的保证金管理功能。
